Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker3
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker3.log
-srvPort
53559
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [25804]  Target information:

Player connection [25804]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 505886517 [EditorId] 505886517 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25804]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 505886517 [EditorId] 505886517 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [25804] Host joined multi-casting on [***********:54997]...
Player connection [25804] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56752
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.008479 seconds.
- Loaded All Assemblies, in  0.729 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 398 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.051 seconds
Domain Reload Profiling: 1776ms
	BeginReloadAssembly (255ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (94ms)
	LoadAllAssembliesAndSetupDomain (287ms)
		LoadAssemblies (250ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (281ms)
			TypeCache.Refresh (278ms)
				TypeCache.ScanAssembly (251ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1051ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (990ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (547ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (98ms)
			ProcessInitializeOnLoadAttributes (215ms)
			ProcessInitializeOnLoadMethodAttributes (123ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.575 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.288 seconds
Domain Reload Profiling: 2858ms
	BeginReloadAssembly (312ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (1121ms)
		LoadAssemblies (720ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (590ms)
			TypeCache.Refresh (442ms)
				TypeCache.ScanAssembly (396ms)
			BuildScriptInfoCaches (124ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1289ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1073ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (188ms)
			ProcessInitializeOnLoadAttributes (747ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 2.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 143 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6405 unused Assets / (5.8 MB). Loaded Objects now: 6987.
Memory consumption went from 244.8 MB to 239.0 MB.
Total: 56.898100 ms (FindLiveObjects: 6.107400 ms CreateObjectMapping: 5.462500 ms MarkObjects: 33.892800 ms  DeleteObjects: 11.428600 ms)

========================================================================
Received Import Request.
  Time since last request: 23951.188313 seconds.
  path: Assets/RealisticCarControllerV4/Physics Materials/RCC_SandPhysics.physicMaterial
  artifactKey: Guid(1d81794eea45227458aa2965af4b201d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RealisticCarControllerV4/Physics Materials/RCC_SandPhysics.physicMaterial using Guid(1d81794eea45227458aa2965af4b201d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50eeca564a3b9cc45bb4c0368f9ee0b2') in 0.2205299 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/RealisticCarControllerV4/Physics Materials/RCC_TerrainPhysics.physicMaterial
  artifactKey: Guid(ed1ad05cd18787943901e6411fdf1e1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RealisticCarControllerV4/Physics Materials/RCC_TerrainPhysics.physicMaterial using Guid(ed1ad05cd18787943901e6411fdf1e1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9130d92a7419e11d2e06f7de4abf718c') in 0.0030137 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000069 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Physics/TrainWheel.physicMaterial
  artifactKey: Guid(be56d7b018ef76f4691f2dff13b548a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Physics/TrainWheel.physicMaterial using Guid(be56d7b018ef76f4691f2dff13b548a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5c7772a1b95b65f3aa378a52868cbb31') in 0.0033451 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/RealisticCarControllerV4/Physics Materials/RCC_TerrainPhysics4.physicMaterial
  artifactKey: Guid(890f038be3e5f474b8362b23be7dd1dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RealisticCarControllerV4/Physics Materials/RCC_TerrainPhysics4.physicMaterial using Guid(890f038be3e5f474b8362b23be7dd1dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b287fcdf7a72d8f4cfc1013b5c06439') in 0.0027144 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000098 seconds.
  path: Assets/RealisticCarControllerV4/Physics Materials/RCC_ZeroFriction.physicMaterial
  artifactKey: Guid(dc74e74acd48f9d4d96a023f2bb05a4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RealisticCarControllerV4/Physics Materials/RCC_ZeroFriction.physicMaterial using Guid(dc74e74acd48f9d4d96a023f2bb05a4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '76800758eda7ec001b570b0bed7ecb76') in 0.0016096 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000340 seconds.
  path: Assets/RealisticCarControllerV4/Physics Materials/RCC_TerrainPhysics1.physicMaterial
  artifactKey: Guid(34d94011ab46ffe4c9347f4cbab45a9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/RealisticCarControllerV4/Physics Materials/RCC_TerrainPhysics1.physicMaterial using Guid(34d94011ab46ffe4c9347f4cbab45a9c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '751c2f1915a7d1ad8c7fd90593f59c5c') in 0.0014859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/MSK 2.2/Standard Assets/Physic Materials/Bike.physicmaterial
  artifactKey: Guid(5410a64fad11e8d6d00011d98d76c639) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Standard Assets/Physic Materials/Bike.physicmaterial using Guid(5410a64fad11e8d6d00011d98d76c639) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bd3c402c71cabe2c0a97ba5a42b98ce8') in 0.0019069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/Physics/AiroplanePhysics.physicMaterial
  artifactKey: Guid(b36dbcce175379040bcc4ad1b12a3c7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Physics/AiroplanePhysics.physicMaterial using Guid(b36dbcce175379040bcc4ad1b12a3c7b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f06098953486c70829810d34fc0942b') in 0.0013313 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/MSK 2.2/Standard Assets/Physic Materials/Wood.physicmaterial
  artifactKey: Guid(cdf7d3f1ad11f8d6d00011d98d76c639) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Standard Assets/Physic Materials/Wood.physicmaterial using Guid(cdf7d3f1ad11f8d6d00011d98d76c639) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'de6015689261979dd985cdddb6a71889') in 0.0014138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000147 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/PhysicsMaterials/Wood.physicMaterial
  artifactKey: Guid(0151b31279cbbe64db23577e9163731a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/PhysicsMaterials/Wood.physicMaterial using Guid(0151b31279cbbe64db23577e9163731a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e685d2aa136720cf4beadb9cd00e757') in 0.0010037 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.570 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.266 seconds
Domain Reload Profiling: 2837ms
	BeginReloadAssembly (366ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (99ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (1067ms)
		LoadAssemblies (806ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (448ms)
			TypeCache.Refresh (28ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (393ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1267ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1046ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (215ms)
			ProcessInitializeOnLoadAttributes (688ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader Hidden/ProbeVolume/VoxelizeScene is not supported: GPU does not support conservative rasterization
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 42 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6403 unused Assets / (5.7 MB). Loaded Objects now: 6992.
Memory consumption went from 222.8 MB to 217.1 MB.
Total: 15.523300 ms (FindLiveObjects: 2.059300 ms CreateObjectMapping: 1.204200 ms MarkObjects: 7.367100 ms  DeleteObjects: 4.890700 ms)

Prepare: number of updated asset objects reloaded= 0
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0