//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2024 BoneCracker Games
// https://www.bonecrackergames.com
// <PERSON><PERSON><PERSON>
//
// Enhanced Trailer Suspension Editor
// Drone Camera Develop by <PERSON>
//----------------------------------------------

using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(RCC_TruckTrailer))]
public class RCC_EnhancedTruckTrailerEditor : Editor {

    RCC_TruckTrailer trailer;
    GUIStyle headerStyle;
    GUIStyle brandingStyle;

    private void OnEnable() {
        trailer = (RCC_TruckTrailer)target;
    }

    public override void OnInspectorGUI() {
        
        serializedObject.Update();

        // Initialize styles
        if (headerStyle == null) {
            headerStyle = new GUIStyle(GUI.skin.label);
            headerStyle.fontStyle = FontStyle.Bold;
            headerStyle.fontSize = 14;
            headerStyle.normal.textColor = Color.white;
        }

        if (brandingStyle == null) {
            brandingStyle = new GUIStyle(GUI.skin.label);
            brandingStyle.fontStyle = FontStyle.BoldAndItalic;
            brandingStyle.fontSize = 12;
            brandingStyle.normal.textColor = new Color(0.3f, 0.8f, 1f);
            brandingStyle.alignment = TextAnchor.MiddleCenter;
        }

        // Branding
        EditorGUILayout.Space(10);
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("🚛 Enhanced Trailer Suspension System", brandingStyle);
        EditorGUILayout.LabelField("Drone Camera Develop by Ali Taj", brandingStyle);
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space(10);

        // Stability Settings
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("🎯 Stability Settings", headerStyle);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("stabilityMultiplier"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("speedBasedStiffness"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxStabilitySpeed"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("angularDragMultiplier"));
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(5);

        // Realistic Suspension Settings
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("🔧 Realistic Suspension Settings (12-Tire Trailer)", headerStyle);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("suspensionSpring"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("suspensionDamping"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("suspensionDistance"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("forceAppPoint"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("wheelMass"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("wheelDampingRate"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("suspensionTargetPosition"));
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(5);

        // Load-Based Suspension
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("📦 Load-Based Suspension", headerStyle);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("enableLoadBasedSuspension"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("currentLoad"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxTrailerLoad"));
        
        // Load percentage display
        if (trailer.maxTrailerLoad > 0) {
            float loadPercentage = (trailer.currentLoad / trailer.maxTrailerLoad) * 100f;
            EditorGUILayout.LabelField($"Load Percentage: {loadPercentage:F1}%");
            
            // Load bar
            Rect loadBarRect = GUILayoutUtility.GetRect(0, 20, GUILayout.ExpandWidth(true));
            EditorGUI.ProgressBar(loadBarRect, loadPercentage / 100f, $"{loadPercentage:F1}%");
        }
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(5);

        // Anti-Roll & Stability
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("⚖️ Anti-Roll & Stability (12-Tire System)", headerStyle);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("enableAntiRoll"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("antiRollForce"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("lateralStabilityForce"));
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(5);

        // Trailer Wheels
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("🛞 Trailer Wheels", headerStyle);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("trailerWheels"), true);
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(5);

        // Other Settings
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("⚙️ Other Settings", headerStyle);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("COM"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("legs"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("brakeWhenDetached"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("brakeForce"));
        EditorGUILayout.EndVertical();

        EditorGUILayout.Space(10);

        // Runtime Information
        if (Application.isPlaying && trailer != null) {
            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("📊 Runtime Information", headerStyle);
            EditorGUILayout.LabelField($"Attached: {trailer.attached}");
            EditorGUILayout.LabelField($"Suspension Compression: {trailer.GetSuspensionCompression():F2}");
            
            if (trailer.attached) {
                EditorGUILayout.LabelField($"Speed: {(trailer.GetComponent<Rigidbody>().linearVelocity.magnitude * 3.6f):F1} km/h");
            }
            EditorGUILayout.EndVertical();
        }

        // Quick Load Presets
        EditorGUILayout.Space(5);
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("🎛️ Quick Load Presets", headerStyle);
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("Empty (0%)")) {
            trailer.SetTrailerLoad(0f);
        }
        if (GUILayout.Button("Quarter (25%)")) {
            trailer.SetTrailerLoad(trailer.maxTrailerLoad * 0.25f);
        }
        if (GUILayout.Button("Half (50%)")) {
            trailer.SetTrailerLoad(trailer.maxTrailerLoad * 0.5f);
        }
        if (GUILayout.Button("Full (100%)")) {
            trailer.SetTrailerLoad(trailer.maxTrailerLoad);
        }
        
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.EndVertical();

        // Tire Configuration Presets
        EditorGUILayout.Space(5);
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField("🔧 Tire Configuration Presets", headerStyle);
        EditorGUILayout.LabelField("Configure suspension settings based on tire count:");
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("4 Tires")) {
            trailer.ConfigureForTireCount(4);
        }
        if (GUILayout.Button("6 Tires")) {
            trailer.ConfigureForTireCount(6);
        }
        if (GUILayout.Button("8 Tires")) {
            trailer.ConfigureForTireCount(8);
        }
        if (GUILayout.Button("10 Tires")) {
            trailer.ConfigureForTireCount(10);
        }
        if (GUILayout.Button("12 Tires")) {
            trailer.ConfigureForTireCount(12);
        }

        EditorGUILayout.EndHorizontal();
        EditorGUILayout.EndVertical();

        serializedObject.ApplyModifiedProperties();

        if (GUI.changed) {
            EditorUtility.SetDirty(trailer);
        }
    }
}
